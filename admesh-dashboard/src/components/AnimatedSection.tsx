"use client";

import { motion, Variants } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { ReactNode } from "react";
import {
  fadeInUp,
  fadeInLeft,
  fadeInRight,
  scaleIn,
  slideInUp,
  staggerContainer
  // intersectionOptions - available for future use
} from "@/lib/animations";

interface AnimatedSectionProps {
  children: ReactNode;
  animation?: "fadeInUp" | "fadeInLeft" | "fadeInRight" | "scaleIn" | "slideInUp" | "stagger";
  delay?: number;
  duration?: number;
  className?: string;
  id?: string;
  threshold?: number;
  triggerOnce?: boolean;
  staggerChildren?: boolean;
  staggerDelay?: number;
}

const animationVariants: Record<string, Variants> = {
  fadeInUp,
  fadeInLeft,
  fadeInRight,
  scaleIn,
  slideInUp,
  stagger: staggerContainer
};

export function AnimatedSection({
  children,
  animation = "fadeInUp",
  delay = 0,
  duration = 0.5,
  className = "",
  id,
  threshold = 0.1,
  triggerOnce = true,
  staggerChildren = false,
  staggerDelay = 0.1
}: AnimatedSectionProps) {
  const { ref, inView } = useInView({
    threshold,
    triggerOnce,
    rootMargin: "-50px 0px"
  });

  const selectedVariant = animationVariants[animation];

  // Create custom variant with delay and duration
  const customVariant: Variants = {
    hidden: selectedVariant.hidden,
    visible: {
      ...selectedVariant.visible,
      transition: {
        ...selectedVariant.visible.transition,
        delay,
        duration,
        ...(staggerChildren && {
          staggerChildren: staggerDelay,
          delayChildren: delay
        })
      }
    }
  };

  return (
    <motion.div
      ref={ref}
      id={id}
      className={className}
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      variants={customVariant}
    >
      {children}
    </motion.div>
  );
}

// Default export for backward compatibility
export default AnimatedSection;

// Specialized components for common use cases
export function AnimatedHero({ children, className = "" }: { children: ReactNode; className?: string }) {
  return (
    <AnimatedSection
      animation="fadeInUp"
      delay={0.2}
      duration={0.8}
      className={className}
      threshold={0.2}
    >
      {children}
    </AnimatedSection>
  );
}

export function AnimatedCard({ 
  children, 
  className = "", 
  delay = 0,
  index = 0 
}: { 
  children: ReactNode; 
  className?: string; 
  delay?: number;
  index?: number;
}) {
  return (
    <motion.div
      className={className}
      initial={{ opacity: 0, y: 30, scale: 0.95 }}
      whileInView={{ 
        opacity: 1, 
        y: 0, 
        scale: 1,
        transition: { 
          duration: 0.5, 
          delay: delay + (index * 0.1),
          ease: [0.0, 0.0, 0.2, 1]
        }
      }}
      whileHover={{ 
        y: -5, 
        scale: 1.02,
        boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
        transition: { duration: 0.3 }
      }}
      viewport={{ once: true, margin: "-50px" }}
    >
      {children}
    </motion.div>
  );
}

export function AnimatedButton({ 
  children, 
  className = "",
  variant = "default"
}: { 
  children: ReactNode; 
  className?: string;
  variant?: "default" | "pulse" | "bounce";
}) {
  const variants = {
    default: {
      rest: { scale: 1, y: 0 },
      hover: { scale: 1.05, y: -2 },
      tap: { scale: 0.95 }
    },
    pulse: {
      rest: { scale: 1 },
      hover: { scale: 1.05 },
      tap: { scale: 0.95 },
      pulse: { 
        scale: [1, 1.05, 1],
        transition: { duration: 1.5, repeat: Infinity }
      }
    },
    bounce: {
      rest: { scale: 1, y: 0 },
      hover: { 
        scale: 1.1, 
        y: -5,
        transition: { type: "spring", stiffness: 400, damping: 10 }
      },
      tap: { scale: 0.9 }
    }
  };

  return (
    <motion.div
      className={className}
      variants={variants[variant]}
      initial="rest"
      whileHover="hover"
      whileTap="tap"
      animate={variant === "pulse" ? "pulse" : "rest"}
    >
      {children}
    </motion.div>
  );
}

export function AnimatedIcon({ 
  children, 
  className = "",
  animation = "float"
}: { 
  children: ReactNode; 
  className?: string;
  animation?: "float" | "spin" | "bounce" | "pulse";
}) {
  const animations = {
    float: {
      y: [-5, 5, -5],
      transition: { duration: 3, repeat: Infinity, ease: "easeInOut" }
    },
    spin: {
      rotate: 360,
      transition: { duration: 2, repeat: Infinity, ease: "linear" }
    },
    bounce: {
      y: [0, -10, 0],
      transition: { duration: 1, repeat: Infinity, ease: "easeInOut" }
    },
    pulse: {
      scale: [1, 1.1, 1],
      transition: { duration: 2, repeat: Infinity, ease: "easeInOut" }
    }
  };

  return (
    <motion.div
      className={className}
      animate={animations[animation]}
    >
      {children}
    </motion.div>
  );
}

export function AnimatedText({ 
  children, 
  className = "",
  animation = "fadeInUp",
  delay = 0
}: { 
  children: ReactNode; 
  className?: string;
  animation?: "fadeInUp" | "typewriter" | "reveal";
  delay?: number;
}) {
  const { ref, inView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
    rootMargin: "-20px 0px"
  });

  const animations = {
    fadeInUp: {
      hidden: { opacity: 0, y: 20 },
      visible: { 
        opacity: 1, 
        y: 0,
        transition: { duration: 0.6, delay, ease: [0.0, 0.0, 0.2, 1] }
      }
    },
    typewriter: {
      hidden: { width: 0 },
      visible: { 
        width: "auto",
        transition: { duration: 1.5, delay, ease: "linear" }
      }
    },
    reveal: {
      hidden: { 
        opacity: 0,
        clipPath: "inset(0 100% 0 0)"
      },
      visible: { 
        opacity: 1,
        clipPath: "inset(0 0% 0 0)",
        transition: { duration: 0.8, delay, ease: [0.0, 0.0, 0.2, 1] }
      }
    }
  };

  return (
    <motion.div
      ref={ref}
      className={className}
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      variants={animations[animation]}
    >
      {children}
    </motion.div>
  );
}

export function AnimatedStagger({ 
  children, 
  className = "",
  staggerDelay = 0.1,
  delay = 0
}: { 
  children: ReactNode; 
  className?: string;
  staggerDelay?: number;
  delay?: number;
}) {
  const { ref, inView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
    rootMargin: "-50px 0px"
  });

  return (
    <motion.div
      ref={ref}
      className={className}
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      variants={{
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            staggerChildren: staggerDelay,
            delayChildren: delay
          }
        }
      }}
    >
      {children}
    </motion.div>
  );
}

export function AnimatedChild({ 
  children, 
  className = ""
}: { 
  children: ReactNode; 
  className?: string;
}) {
  return (
    <motion.div
      className={className}
      variants={{
        hidden: { opacity: 0, y: 20 },
        visible: { 
          opacity: 1, 
          y: 0,
          transition: { duration: 0.5, ease: [0.0, 0.0, 0.2, 1] }
        }
      }}
    >
      {children}
    </motion.div>
  );
}
