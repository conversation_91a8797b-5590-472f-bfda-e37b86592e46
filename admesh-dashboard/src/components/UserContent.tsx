"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { useState, useEffect, Suspense } from "react";
import AuthModal from "@/components/AuthModal";
import MobileNav from "@/components/MobileNav";
import ChatBox from "@/components/ChatBox";
// import { useInView } from "react-intersection-observer"; // Available for future use
import { ArrowR<PERSON>, Shield, Zap, Brain, Gift } from "lucide-react";
import Image from "next/image";
import { useAuth } from "@/hooks/use-auth";
import { useSearchParams } from "next/navigation";
import {
  AnimatedSection,
  AnimatedCard,
  AnimatedButton,
  AnimatedIcon,
  AnimatedText,
  AnimatedStagger,
  AnimatedChild
} from "@/components/AnimatedSection";

type Role = "user" | "brand" | "agent" | null;

interface UserContentProps {
  onRoleChange: (role: Role) => void;
}

// SearchParamsHandler component to handle search params logic
function SearchParamsHandler() {
  const searchParams = useSearchParams();

  // Store AdMesh tracking parameters in localStorage
  useEffect(() => {
    // Get tracking parameters from URL
    const clickId = searchParams.get('utm_click_id') || searchParams.get('clickId');
    const source = searchParams.get('utm_source') || searchParams.get('source');
    const target = searchParams.get('utm_target') || searchParams.get('target');

    // Log all URL parameters for debugging
    console.log('URL parameters:', Object.fromEntries([...searchParams.entries()]));

    // Check if we have the necessary tracking parameters
    if (clickId) {
      // Be more lenient with source check - either 'admesh' or not specified
      if (!source || source.toLowerCase() === 'admesh') {
        // Use target if available, otherwise use a default value
        const targetValue = target || 'default';
        const key = `admesh_referral_${targetValue}`;
        const trackingData = {
          clickId,
          source: source || 'localhost',
          target: targetValue,
          timestamp: Date.now(),
          test: searchParams.get('test') === 'true'
        };

        // Store in localStorage
        localStorage.setItem(key, JSON.stringify(trackingData));
        console.log('AdMesh tracking parameters stored:', trackingData);

        // Also store in sessionStorage as a backup
        sessionStorage.setItem(key, JSON.stringify(trackingData));

        // Also store in a generic key for easier access
        localStorage.setItem('admesh_last_referral', JSON.stringify(trackingData));

        // Show a notification for test mode
        if (trackingData.test) {
          console.log('Test tracking parameters detected and stored');
          // We'll just use console.log for now since we don't have direct access to toast
        }
      }
    }
  }, [searchParams]);

  return null;
}

export default function UserContent({ /* onRoleChange */ }: UserContentProps) {
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);
  const { user } = useAuth();

  // Track scroll progress for animations
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.body.offsetHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      setScrollProgress(scrollPercent);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // InView hooks for each section (for future use)
  // const { ref: heroRef, inView: heroInView } = useInView({ triggerOnce: true });
  // const { ref: journeyRef, inView: journeyInView } = useInView({ triggerOnce: true }); // Available for future use
  // const { ref: vaultRef, inView: vaultInView } = useInView({ triggerOnce: true }); // Available for future use
  // Using heroInView for CTA section since we removed testimonials
  // const testimonialsInView = heroInView; // Available for future use

  const journeySteps = [
    {
      icon: <div className="bg-primary rounded-full p-3"><Brain className="w-6 h-6 text-primary-foreground" /></div>,
      title: "Create Your Agent",
      description: "Launch your personal agent instantly — unique to you.",
    },
    {
      icon: <div className="bg-primary rounded-full p-3"><Zap className="w-6 h-6 text-primary-foreground" /></div>,
      title: "Train by Asking Queries",
      description: "Every search you make teaches your agent what you value.",
    },
    {
      icon: <div className="bg-primary rounded-full p-3"><Gift className="w-6 h-6 text-primary-foreground" /></div>,
      title: "Discover Offers and Rewards",
      description: "Your agent finds products, deals, and rewards tailored to your needs.",
    },
    {
      icon: <div className="bg-primary rounded-full p-3"><Shield className="w-6 h-6 text-primary-foreground" /></div>,
      title: "Earn As You Act",
      description: "Engage with offers and start unlocking real rewards through your agent.",
    },
  ];

  // Removed testimonials array

  const scrollToSection = (sectionId: string) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Get the current pathname
  const pathname = typeof window !== 'undefined' ? window.location.pathname : '';

  // Determine if we should show the Pioneer Program nav item
  const showPioneerProgram = !pathname.startsWith('/brand') && !pathname.startsWith('/agent');

  // Create nav items array based on condition
  const navItems = [
    { label: "Home", sectionId: "hero-section" },
    { label: "Journey", sectionId: "journey-section" }
  ];

  // Add Pioneer Program nav item if it should be shown
  if (showPioneerProgram) {
    navItems.push({ label: "Pioneer Program", sectionId: "agent-pioneer-section" });
    navItems.push({ label: "Join Now", sectionId: "cta-section" });
  }

  return (
    <div className="flex flex-col items-center w-full">
      {/* Suspense boundary for search params */}
      <Suspense fallback={null}>
        <SearchParamsHandler />
      </Suspense>

      {/* Mobile-friendly Navigation */}
      <MobileNav
        items={navItems}
        activeSection={scrollProgress > 50 && showPioneerProgram ? "agent-pioneer-section" :
                      scrollProgress > 25 ? "journey-section" : "hero-section"}
        scrollToSection={scrollToSection}
        onGetStarted={() => setIsAuthModalOpen(true)}
        themeColor="blue"
        user={user}
      />

      {/* Hero Section */}
      <AnimatedSection
        id="hero-section"
        animation="fadeInUp"
        className="w-full min-h-screen grid grid-cols-1 md:grid-cols-2 overflow-hidden bg-background"
        threshold={0.2}
      >
        {/* Left side: Hero content */}
        <div className="flex items-center justify-center px-6 py-12">
          <AnimatedStagger className="text-center md:text-left max-w-xl w-full" staggerDelay={0.15} delay={0.2}>
            <AnimatedChild>
              <motion.span
                className="inline-block mb-2 px-3 py-1 bg-muted text-foreground rounded-full text-sm font-medium"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Early Access
              </motion.span>
            </AnimatedChild>

            <AnimatedChild>
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold tracking-tight leading-tight mb-6 text-foreground">
                Your Personal Agent That Discovers and Earns For You
              </h1>
            </AnimatedChild>

            <AnimatedChild>
              <p className="text-md sm:text-lg text-muted-foreground max-w-2xl mx-auto md:mx-0 mb-10">
                Every query you ask grows your personal agent.
                It finds products, unlocks offers, and earns you rewards over time.
              </p>
            </AnimatedChild>

            <AnimatedChild>
              <div className="flex flex-col sm:flex-row justify-center md:justify-start gap-4">
                <AnimatedButton variant="bounce">
                  <Button
                    size="lg"
                    className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium shadow-md hover:shadow-lg transition-all duration-300"
                    onClick={() => setIsAuthModalOpen(true)}
                  >
                    Build Your Agent
                  </Button>
                </AnimatedButton>

                <AnimatedButton>
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-border hover:bg-muted flex items-center gap-2 hover:gap-3 transition-all duration-300"
                    onClick={() => scrollToSection('journey-section')}
                  >
                    Learn More <ArrowRight className="w-4 h-4" />
                  </Button>
                </AnimatedButton>
              </div>
            </AnimatedChild>

            <AnimatedChild>
              <div className="mt-8 flex items-center justify-center md:justify-start gap-4">
                {/* Additional content can go here */}
              </div>
            </AnimatedChild>
          </AnimatedStagger>
        </div>

        {/* Right side: ChatBox (only for desktop) */}
        {!isMobile ? (
          <div className="w-full flex items-center justify-center bg-background px-6 py-14">
            <AnimatedSection
              animation="scaleIn"
              delay={0.4}
              duration={0.8}
              className="w-full shadow-lg rounded-xl overflow-hidden"
            >
              <ChatBox />
            </AnimatedSection>
          </div>
        ) : null}
      </AnimatedSection>
{/*
      <section className="w-full py-8 bg-white border-y border-gray-100">
        <div className="max-w-6xl mx-auto grid grid-cols-2 md:grid-cols-4 gap-6 px-4">
          {[
            { label: "Agents Created", value: "25,000+" },
            { label: "Reward Points Generated", value: "1.5M+" },
            { label: "Average User Earnings", value: "$38/mo" },
            { label: "Satisfaction Rate", value: "97%" },
          ].map((stat, idx) => (
            <motion.div
              key={idx}
              className="text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: heroInView ? 1 : 0, y: heroInView ? 0 : 20 }}
              transition={{ duration: 0.5, delay: 0.1 * idx + 0.5 }}
            >
              <p className="text-2xl md:text-3xl font-bold text-gray-900">{stat.value}</p>
              <p className="text-sm text-muted-foreground">{stat.label}</p>
            </motion.div>
          ))}
        </div>
      </section> */}

      {/* Journey Section */}
      <AnimatedSection
        id="journey-section"
        animation="fadeInUp"
        className="w-full min-h-screen flex items-center justify-center bg-background px-4 py-20"
      >
        <div className="max-w-6xl mx-auto text-center">
          <AnimatedText
            animation="fadeInUp"
            className="mb-16"
            delay={0.2}
          >
            <span className="text-sm font-medium text-muted-foreground block mb-3">HOW IT WORKS</span>
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">Your Agent&apos;s Journey Starts With You</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Training your agent is simple, rewarding, and personalized to your needs.
            </p>
          </AnimatedText>

          <AnimatedStagger className="grid grid-cols-1 md:grid-cols-4 gap-8 relative" staggerDelay={0.15} delay={0.4}>
            {journeySteps.map((step, index) => (
              <AnimatedCard
                key={index}
                index={index}
                className="flex flex-col items-center text-center relative bg-card rounded-xl p-6 shadow-sm border border-border group cursor-pointer"
              >
                <AnimatedIcon
                  animation="bounce"
                  className="rounded-full bg-muted p-4 mb-4 group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-300"
                >
                  {step.icon}
                </AnimatedIcon>
                <h3 className="text-xl font-semibold mb-2 text-foreground group-hover:text-primary transition-colors duration-300">{step.title}</h3>
                <p className="text-muted-foreground group-hover:text-foreground transition-colors duration-300">{step.description}</p>

                {/* Arrow only between steps */}
                {index < journeySteps.length - 1 && (
                  <AnimatedIcon
                    animation="float"
                    className="hidden md:block absolute right-[-20px] top-1/2 transform -translate-y-1/2"
                  >
                    <ArrowRight className="w-6 h-6 text-muted-foreground" />
                  </AnimatedIcon>
                )}
              </AnimatedCard>
            ))}
          </AnimatedStagger>

          <AnimatedSection animation="fadeInUp" delay={0.8} className="mt-12">
            <AnimatedButton variant="pulse">
              <Button
                size="lg"
                className="bg-primary hover:bg-primary/90 text-primary-foreground transition-all duration-300"
                onClick={() => setIsAuthModalOpen(true)}
              >
                Start Your Journey
              </Button>
            </AnimatedButton>
          </AnimatedSection>
        </div>
      </AnimatedSection>

{/* Agent Pioneer Rewards Section */}
<AnimatedSection
  id="agent-pioneer-section"
  animation="fadeInUp"
  className="w-full min-h-screen flex items-center justify-center bg-muted px-4 py-20"
>
  <div className="max-w-6xl mx-auto">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">

      {/* Left: Content */}
      <AnimatedSection
        animation="fadeInLeft"
        delay={0.2}
        className="text-center md:text-left"
      >
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
          Introducing the Agent Pioneer Program
        </h2>

        <p className="text-lg text-muted-foreground mb-6">
          Become one of the first users to shape how AI agents discover, recommend, and reward across the AdMesh network.
          Your early actions will train smarter agents — and unlock exclusive benefits.
        </p>

        <p className="text-sm text-muted-foreground italic mb-6">
          Agent Pioneers help build a better discovery protocol for everyone. You&apos;re not just earning rewards — you&apos;re setting the foundation for the future of intent-based discovery.
        </p>

        <AnimatedStagger className="text-muted-foreground mb-8 space-y-4 text-left" staggerDelay={0.1} delay={0.4}>
          {[
            "Earn 50% more per successful recommendation",
            "Get priority visibility when your agent recommends products",
            "Unlock early access to premium offers and new features",
            "Display a permanent Agent Pioneer badge on your profile"
          ].map((item, idx) => (
            <AnimatedChild key={idx}>
              <motion.li
                className="flex items-start gap-3 group"
                whileHover={{ x: 5 }}
                transition={{ duration: 0.2 }}
              >
                <AnimatedIcon
                  animation="pulse"
                  className="rounded-full bg-primary/20 p-1 mt-1 group-hover:bg-primary/30 transition-colors duration-300"
                >
                  <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                </AnimatedIcon>
                <span className="group-hover:text-foreground transition-colors duration-300">{item}</span>
              </motion.li>
            </AnimatedChild>
          ))}
        </AnimatedStagger>

        <AnimatedButton variant="bounce">
          <Button
            size="lg"
            className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium mb-6 transition-all duration-300"
            onClick={() => setIsAuthModalOpen(true)}
          >
            Join as an Agent Pioneer
          </Button>
        </AnimatedButton>

      </AnimatedSection>

      {/* Right: Visual Reward Card */}
      <AnimatedCard
        className="card-glass rounded-2xl shadow-xl p-8 cursor-pointer"
        delay={0.4}
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-full bg-card border-2 border-primary flex items-center justify-center">
              <Image src="/logo.svg" alt="AdMesh Logo" width={24} height={24} className="w-6 h-6" />
            </div>
            <div>
              <h3 className="font-bold text-xl text-foreground">Agent Pioneer</h3>
              <p className="text-sm text-muted-foreground">Unlocks at 1000 XP</p>
            </div>
          </div>
          <div className="bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-400 px-3 py-1 rounded-full text-xs font-medium">
            Limited Access
          </div>
        </div>

        <div className="space-y-6">
          {[
            { label: "Earnings Boost", value: "+50% per conversion" },
            { label: "Visibility Advantage", value: "Higher recommendation priority" },
            { label: "Offer Access", value: "Premium offers first" }
          ].map((stat, idx) => (
            <div key={idx}>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-muted-foreground">{stat.label}</span>
                <span className="font-medium text-foreground">{stat.value}</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
                <div className="bg-primary h-2 rounded-full transition-all duration-1000" style={{ width: `${(idx + 1) * 30 + 40}%` }}></div>
              </div>
            </div>
          ))}

          <AnimatedButton variant="pulse">
            <Button
              className="w-full bg-primary hover:bg-primary/90 text-primary-foreground transition-all duration-300"
              onClick={() => setIsAuthModalOpen(true)}
            >
              Claim My Rewards
            </Button>
          </AnimatedButton>

          <p className="text-xs text-center text-muted-foreground mt-2">
          Limited to the first 500 users who reach 1000 XP.
          </p>
        </div>
      </AnimatedCard>
    </div>
  </div>
</AnimatedSection>


      {/* CTA Section */}
      <AnimatedSection
        id="cta-section"
        animation="fadeInUp"
        className="w-full py-16 bg-primary text-primary-foreground"
      >
        <div className="max-w-4xl mx-auto px-4 text-center">
          <AnimatedStagger staggerDelay={0.2} delay={0.3}>
            <AnimatedChild>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-primary-foreground">
                Ready to Become an Agent Pioneer?
              </h2>
            </AnimatedChild>

            <AnimatedChild>
              <p className="text-lg text-primary-foreground/80 mb-8">
                Join hundreds of agent pioneers already creating their personal agents and earning rewards.
              </p>
            </AnimatedChild>

            <AnimatedChild>
              <AnimatedButton variant="pulse">
                <Button
                  size="lg"
                  className="bg-background text-foreground hover:bg-background/90 transition-all duration-300"
                  onClick={() => setIsAuthModalOpen(true)}
                >
                  Become an Agent Pioneer
                </Button>
              </AnimatedButton>
            </AnimatedChild>
          </AnimatedStagger>
        </div>
      </AnimatedSection>

      {/* Auth Modal */}
      <AuthModal
        open={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />
    </div>
  );
}
